# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

import os
import tempfile
from unittest.mock import patch

import pytest

from superset.config import _get_dodo_version


class TestDodoVersion:
    """Test DODO version loading functionality."""

    def test_get_dodo_version_file_exists(self):
        """Test that _get_dodo_version reads version from file when it exists."""
        # Create a temporary version file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('VERSION = "test-version-123"\n')
            temp_file = f.name

        try:
            # Mock the path resolution to point to our temp file
            with patch('os.path.join') as mock_join:
                mock_join.return_value = temp_file
                with patch('os.path.exists', return_value=True):
                    version = _get_dodo_version()
                    assert version == "test-version-123"
        finally:
            os.unlink(temp_file)

    def test_get_dodo_version_file_not_exists(self):
        """Test that _get_dodo_version returns 'local' when file doesn't exist."""
        with patch('os.path.exists', return_value=False):
            version = _get_dodo_version()
            assert version == "local"

    def test_get_dodo_version_file_read_error(self):
        """Test that _get_dodo_version returns 'local' when file read fails."""
        with patch('os.path.exists', return_value=True):
            with patch('builtins.open', side_effect=IOError("File read error")):
                version = _get_dodo_version()
                assert version == "local"

    def test_get_dodo_version_malformed_file(self):
        """Test that _get_dodo_version handles malformed version files."""
        # Create a temporary malformed version file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('# This is a comment\nSOME_OTHER_VAR = "not version"\n')
            temp_file = f.name

        try:
            with patch('os.path.join') as mock_join:
                mock_join.return_value = temp_file
                with patch('os.path.exists', return_value=True):
                    version = _get_dodo_version()
                    assert version == "local"
        finally:
            os.unlink(temp_file)

    def test_get_dodo_version_different_quote_styles(self):
        """Test that _get_dodo_version handles different quote styles."""
        test_cases = [
            ('VERSION = "double-quotes"', "double-quotes"),
            ("VERSION = 'single-quotes'", "single-quotes"),
            ('VERSION="no-spaces"', "no-spaces"),
            ("VERSION='mixed-style'", "mixed-style"),
        ]

        for file_content, expected_version in test_cases:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(file_content + '\n')
                temp_file = f.name

            try:
                with patch('os.path.join') as mock_join:
                    mock_join.return_value = temp_file
                    with patch('os.path.exists', return_value=True):
                        version = _get_dodo_version()
                        assert version == expected_version
            finally:
                os.unlink(temp_file)
